import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { z } from 'zod/v4';
import { BaseQueueService, QueueNameEnum, QueuePriorityType } from '@app/core/queue';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { QUEUE_PRIORITY_MAP } from '@app/core/queue/queue.config';
import { SymbolDownloadJobDataSchema, SymbolDownloadJobData, SymbolConstants } from './symbol.schema';

export type SymbolDownloadJobDataType = SymbolDownloadJobData;

/**
 * Symbol download queue service for daily symbol master downloads
 *
 * Manages symbol master data download jobs with features:
 * - Daily cron job scheduling at 8:00 AM IST for all exchanges at once
 * - On-demand download capabilities
 * - Job status tracking and monitoring
 * - Comprehensive error handling and retry mechanisms
 * - Simplified single-URL download approach
 */
@Injectable()
export class SymbolDownloadQueueService extends BaseQueueService<SymbolDownloadJobDataType> implements OnModuleInit {
  private readonly cronJobId = 'daily-symbol-master-download';

  constructor(
    @InjectQueue(QueueNameEnum.enum.SYMBOL_DOWNLOAD) queue: Queue<SymbolDownloadJobDataType>,
    private readonly errorUtils: ErrorUtilsService,
    protected readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    super(queue, dateTimeUtils);
  }

  /**
   * Initialize the queue service and set up cron jobs
   */
  async onModuleInit(): Promise<void> {
    try {
      this.logger.log('Initializing Symbol Download Queue Service');

      // Set up daily cron job for symbol master download
      await this.setupDailyCronJob();

      this.logger.log('Symbol Download Queue Service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Symbol Download Queue Service', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== CRON JOB SETUP ====================

  /**
   * Set up daily cron job for symbol master download at 8:00 AM IST
   */
  private async setupDailyCronJob(): Promise<void> {
    try {
      this.logger.log('Setting up daily symbol master download cron job');

      // Remove existing cron job if it exists
      await this.removeCronJob();

      // Create new cron job
      const job = await this.addRepeatingJob(
        this.cronJobId,
        {
          exchange: 'ALL',
          segment: 'ALL',
          forceRefresh: true,
          batchSize: SymbolConstants.DefaultBatchSize,
          requestId: `cron-${this.dateTimeUtils.getUtcNow()}`,
          scheduledAt: this.dateTimeUtils.getUtcNow(),
          priority: 1, // Highest priority for scheduled jobs
        },
        {
          pattern: SymbolConstants.CronSchedule, // '0 8 * * *' - 8:00 AM daily
          tz: SymbolConstants.CronTimezone, // 'Asia/Kolkata'
        },
        {
          priority: 1, // High priority
          removeOnComplete: 10, // Keep last 10 completed jobs
          removeOnFail: 5, // Keep last 5 failed jobs
        },
      );

      this.logger.log('Daily symbol master download cron job scheduled successfully', {
        jobId: job.id,
        schedule: SymbolConstants.CronSchedule,
        timezone: SymbolConstants.CronTimezone,
      });
    } catch (error) {
      this.logger.error('Failed to set up daily cron job', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Remove existing cron job
   */
  async removeCronJob(): Promise<void> {
    try {
      // Use the queue's removeRepeatable method to remove the cron job
      await this.queue.removeRepeatable(this.cronJobId, {
        pattern: SymbolConstants.CronSchedule,
        tz: SymbolConstants.CronTimezone,
      });
      this.logger.log('Removed existing cron job');
    } catch {
      // Job might not exist, which is fine
      this.logger.debug('No existing cron job to remove');
    }
  }

  // ==================== JOB CREATION METHODS ====================

  /**
   * Add daily symbol download job for all exchanges
   */
  async addDailySymbolDownloadJob(options?: {
    forceRefresh?: boolean;
    batchSize?: number;
    priority?: QueuePriorityType;
    delay?: number;
    requestId?: string;
  }): Promise<{ jobId: string; requestId: string }> {
    try {
      const requestId = options?.requestId || `daily-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      const jobData: SymbolDownloadJobDataType = {
        exchange: 'ALL',
        segment: 'ALL',
        forceRefresh: options?.forceRefresh || false,
        batchSize: options?.batchSize || SymbolConstants.DefaultBatchSize,
        requestId,
        scheduledAt: this.dateTimeUtils.getUtcNow(),
        priority: options?.priority ? QUEUE_PRIORITY_MAP[options.priority] : 5,
      };

      // Validate job data
      const validatedData = SymbolDownloadJobDataSchema.parse(jobData);

      const jobName = `download-symbols-all-${requestId}`;

      this.logger.log('Adding daily symbol download job', {
        requestId,
        forceRefresh: validatedData.forceRefresh,
        batchSize: validatedData.batchSize,
      });

      const job = options?.priority
        ? await this.addJobWithPriority(jobName, validatedData, options.priority, {
            delay: options.delay,
            removeOnComplete: 50,
            removeOnFail: 20,
          })
        : await this.addJob(jobName, validatedData, {
            delay: options?.delay,
            removeOnComplete: 50,
            removeOnFail: 20,
          });

      const jobId = job.id || 'unknown';

      this.logger.log('Daily symbol download job added successfully', {
        jobId,
        requestId,
      });

      return { jobId, requestId };
    } catch (error) {
      this.logger.error('Failed to add daily symbol download job', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== JOB STATUS AND MONITORING ====================

  /**
   * Get enhanced symbol download job status with detailed information
   */
  async getJobStatus(jobId: string): Promise<{
    id: string;
    name?: string;
    data?: SymbolDownloadJobDataType;
    progress: number;
    state: string;
    attempts: number;
    maxAttempts: number;
    createdAt?: Date;
    processedAt?: Date;
    finishedAt?: Date;
    failedReason?: string;
    result?: unknown;
    timestamp: Date;
  } | null> {
    try {
      const job = await this.getJob(jobId);
      if (!job) {
        return null;
      }

      const state = await job.getState();

      return {
        id: job.id || 'unknown',
        name: job.name,
        data: job.data,
        progress: typeof job.progress === 'number' ? job.progress : 0,
        state,
        attempts: job.attemptsMade,
        maxAttempts: job.opts?.attempts || 3,
        createdAt: job.timestamp ? new Date(job.timestamp) : undefined,
        processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
        finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
        failedReason: job.failedReason,
        result: job.returnvalue,
        timestamp: this.dateTimeUtils.getNewDate(),
      };
    } catch (error) {
      this.logger.error('Failed to get job status', {
        jobId,
        error: this.errorUtils.getErrorMessage(error),
      });
      return null;
    }
  }

  /**
   * Get multiple job statuses by request ID
   */
  async getJobStatusByRequestId(requestId: string): Promise<
    Array<{
      id: string;
      name?: string;
      data?: SymbolDownloadJobDataType;
      progress: number;
      state: string;
      attempts: number;
      timestamp: Date;
    }>
  > {
    try {
      // Get all jobs from different states and filter by request ID
      const [waitingJobs, activeJobs, completedJobs, failedJobs, delayedJobs] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getCompleted(),
        this.queue.getFailed(),
        this.queue.getDelayed(),
      ]);
      const jobs = [...waitingJobs, ...activeJobs, ...completedJobs, ...failedJobs, ...delayedJobs];
      const matchingJobs = jobs.filter((job) => job.data?.requestId === requestId);

      const statuses = await Promise.all(
        matchingJobs.map(async (job) => {
          const state = await job.getState();
          return {
            id: job.id || 'unknown',
            name: job.name,
            data: job.data,
            progress: typeof job.progress === 'number' ? job.progress : 0,
            state,
            attempts: job.attemptsMade,
            timestamp: new Date(),
          };
        }),
      );

      return statuses;
    } catch (error) {
      this.logger.error('Failed to get job status by request ID', {
        requestId,
        error: this.errorUtils.getErrorMessage(error),
      });
      return [];
    }
  }

  /**
   * Get queue statistics and health information
   */
  async getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: number;
    cronJobStatus: 'active' | 'inactive' | 'unknown';
    lastCronRun?: Date;
    nextCronRun?: Date;
    totalProcessed: number;
    avgProcessingTime?: number;
  }> {
    try {
      const healthStatus = await this.getHealthStatus();

      // Check cron job status
      let cronJobStatus: 'active' | 'inactive' | 'unknown' = 'unknown';
      let lastCronRun: Date | undefined;
      let nextCronRun: Date | undefined;

      try {
        // Try to get the repeating job using the queue's getJob method
        const cronJob = await this.getJob(this.cronJobId);
        if (cronJob && cronJob.opts?.repeat) {
          cronJobStatus = 'active';
          // Note: BullMQ RepeatOptions doesn't expose next run time directly
          // This would need to be calculated based on the cron pattern
        } else {
          cronJobStatus = 'inactive';
        }
      } catch (error) {
        this.logger.warn('Failed to get cron job status', {
          error: this.errorUtils.getErrorMessage(error),
        });
      }

      return {
        waiting: healthStatus.waiting,
        active: healthStatus.active,
        completed: healthStatus.completed,
        failed: healthStatus.failed,
        delayed: healthStatus.delayed,
        paused: healthStatus.paused ? 1 : 0,
        cronJobStatus,
        lastCronRun,
        nextCronRun,
        totalProcessed: healthStatus.completed + healthStatus.failed,
        avgProcessingTime: undefined, // Could be calculated from job history
      };
    } catch (error) {
      this.logger.error('Failed to get queue statistics', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== JOB MANAGEMENT ====================

  /**
   * Cancel symbol download job with enhanced logging
   */
  async cancelJob(jobId: string): Promise<boolean> {
    try {
      this.logger.log('Cancelling symbol download job', { jobId });

      const job = await this.getJob(jobId);
      if (!job) {
        this.logger.warn('Job not found for cancellation', { jobId });
        return false;
      }

      await this.removeJob(jobId);

      this.logger.log('Symbol download job cancelled successfully', { jobId });
      return true;
    } catch (error) {
      this.logger.error('Failed to cancel symbol download job', {
        jobId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Cancel all jobs with a specific request ID
   */
  async cancelJobsByRequestId(requestId: string): Promise<number> {
    try {
      this.logger.log('Cancelling jobs by request ID', { requestId });

      // Get jobs from different states using the queue instance
      const [waitingJobs, activeJobs, delayedJobs] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getDelayed(),
      ]);
      const jobs = [...waitingJobs, ...activeJobs, ...delayedJobs];
      const matchingJobs = jobs.filter((job) => job.data?.requestId === requestId);

      let cancelledCount = 0;
      for (const job of matchingJobs) {
        try {
          if (job.id) {
            await this.removeJob(job.id);
            cancelledCount++;
          }
        } catch (error) {
          this.logger.warn('Failed to cancel individual job', {
            jobId: job.id,
            requestId,
            error: this.errorUtils.getErrorMessage(error),
          });
        }
      }

      this.logger.log('Jobs cancelled by request ID', {
        requestId,
        totalFound: matchingJobs.length,
        cancelled: cancelledCount,
      });

      return cancelledCount;
    } catch (error) {
      this.logger.error('Failed to cancel jobs by request ID', {
        requestId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get pending symbol download jobs count
   */
  async getPendingJobsCount(): Promise<number> {
    const healthStatus = await this.getHealthStatus();
    return healthStatus.waiting + healthStatus.delayed;
  }

  /**
   * Get active symbol download jobs count
   */
  async getActiveJobsCount(): Promise<number> {
    const healthStatus = await this.getHealthStatus();
    return healthStatus.active;
  }

  /**
   * Clean old completed and failed jobs with enhanced options
   */
  async cleanOldJobs(options?: {
    olderThanHours?: number;
    keepCompleted?: number;
    keepFailed?: number;
  }): Promise<{ cleaned: number; kept: number }> {
    try {
      const olderThanHours = options?.olderThanHours || 24;
      const keepCompleted = options?.keepCompleted || 50;
      const keepFailed = options?.keepFailed || 20;

      this.logger.log('Cleaning old symbol download jobs', {
        olderThanHours,
        keepCompleted,
        keepFailed,
      });

      const olderThanMs = olderThanHours * 60 * 60 * 1000;

      // Clean the queue (base method returns void)
      await this.cleanQueue(olderThanMs);

      // Since we can't get exact count from base cleanQueue method,
      // we'll return the configured keep counts as an approximation
      const estimatedCleaned = Math.max(0, keepCompleted + keepFailed);

      this.logger.log('Old jobs cleaned successfully', {
        cleaned: estimatedCleaned,
        olderThanHours,
      });

      return {
        cleaned: estimatedCleaned,
        kept: keepCompleted + keepFailed,
      };
    } catch (error) {
      this.logger.error('Failed to clean old jobs', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get comprehensive queue health status
   */
  async getComprehensiveHealthStatus(): Promise<{
    isHealthy: boolean;
    queueStats: {
      waiting: number;
      active: number;
      completed: number;
      failed: number;
      delayed: number;
      paused: number;
      cronJobStatus: 'active' | 'inactive' | 'unknown';
      lastCronRun?: Date;
      nextCronRun?: Date;
      totalProcessed: number;
      avgProcessingTime?: number;
    } | null;
    cronJobStatus: string;
    lastActivity?: Date;
    issues: string[];
  }> {
    try {
      const queueStats = await this.getQueueStats();
      const issues: string[] = [];

      // Check for issues
      if (queueStats.cronJobStatus === 'inactive') {
        issues.push('Daily cron job is not active');
      }

      if (queueStats.failed > queueStats.completed * 0.1) {
        issues.push('High failure rate detected');
      }

      if (queueStats.waiting > 100) {
        issues.push('Large number of waiting jobs');
      }

      const isHealthy = issues.length === 0;

      return {
        isHealthy,
        queueStats,
        cronJobStatus: queueStats.cronJobStatus,
        lastActivity: queueStats.nextCronRun,
        issues,
      };
    } catch (error) {
      this.logger.error('Failed to get comprehensive health status', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        isHealthy: false,
        queueStats: null,
        cronJobStatus: 'unknown',
        issues: ['Failed to get health status'],
      };
    }
  }
}
