import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { BaseQueueService, QueueNameEnum, QueuePriorityType } from '@app/core/queue';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { QUEUE_PRIORITY_MAP } from '@app/core/queue/queue.config';
import { SymbolDownloadJobDataSchema, SymbolDownloadJobData, SymbolConstants } from './symbol.schema';

export type SymbolDownloadJobDataType = SymbolDownloadJobData;

/**
 * Symbol download queue service for daily symbol master downloads
 *
 * Manages symbol master data download jobs with features:
 * - Daily cron job scheduling at 8:00 AM IST for all exchanges at once
 * - On-demand download capabilities
 * - Job status tracking and monitoring
 * - Comprehensive error handling and retry mechanisms
 * - Simplified single-URL download approach
 */
@Injectable()
export class SymbolDownloadQueueService extends BaseQueueService<SymbolDownloadJobDataType> implements OnModuleInit {
  private readonly cronJobId = 'daily-symbol-master-download';

  constructor(
    @InjectQueue(QueueNameEnum.enum.SYMBOL_DOWNLOAD) queue: Queue<SymbolDownloadJobDataType>,
    private readonly errorUtils: ErrorUtilsService,
    protected readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    super(queue, dateTimeUtils);
  }

  /**
   * Initialize the queue service and set up cron jobs
   */
  async onModuleInit(): Promise<void> {
    try {
      this.logger.log('Initializing Symbol Download Queue Service');

      // Set up daily cron job for symbol master download
      await this.setupDailyCronJob();

      this.logger.log('Symbol Download Queue Service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Symbol Download Queue Service', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== CRON JOB SETUP ====================

  /**
   * Set up daily cron job for symbol master download at 8:00 AM IST
   */
  private async setupDailyCronJob(): Promise<void> {
    try {
      this.logger.log('Setting up daily symbol master download cron job');

      // Remove existing cron job if it exists
      await this.removeCronJob();

      // Create new cron job
      const job = await this.addRepeatingJob(
        this.cronJobId,
        {
          exchange: 'ALL',
          segment: 'ALL',
          forceRefresh: true,
          batchSize: SymbolConstants.DefaultBatchSize,
          requestId: `cron-${this.dateTimeUtils.getUtcNow()}`,
          scheduledAt: this.dateTimeUtils.getUtcNow(),
          priority: 1, // Highest priority for scheduled jobs
        },
        {
          pattern: SymbolConstants.CronSchedule, // '0 8 * * *' - 8:00 AM daily
          tz: SymbolConstants.CronTimezone, // 'Asia/Kolkata'
        },
        {
          priority: 1, // High priority
          removeOnComplete: 10, // Keep last 10 completed jobs
          removeOnFail: 5, // Keep last 5 failed jobs
        },
      );

      this.logger.log('Daily symbol master download cron job scheduled successfully', {
        jobId: job.id,
        schedule: SymbolConstants.CronSchedule,
        timezone: SymbolConstants.CronTimezone,
      });
    } catch (error) {
      this.logger.error('Failed to set up daily cron job', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Remove existing cron job
   * Note: Using removeRepeatable method which is still functional in BullMQ v5.56.9
   * TODO: Consider migrating to Job Schedulers when upgrading to newer BullMQ versions
   */
  async removeCronJob(): Promise<void> {
    try {
      // Use the queue's removeRepeatable method to remove the cron job
      // This method is deprecated but still functional in current BullMQ version
      await this.queue.removeRepeatable(this.cronJobId, {
        pattern: SymbolConstants.CronSchedule,
        tz: SymbolConstants.CronTimezone,
      });
      this.logger.log('Removed existing cron job');
    } catch (error) {
      // Job might not exist or removal might fail, which is acceptable during setup
      this.logger.debug('Failed to remove cron job (this is usually fine)', {
        error: this.errorUtils.getErrorMessage(error),
      });
    }
  }

  // ==================== JOB CREATION METHODS ====================

  /**
   * Add daily symbol download job for all exchanges
   */
  async addDailySymbolDownloadJob(options?: {
    forceRefresh?: boolean;
    batchSize?: number;
    priority?: QueuePriorityType;
    delay?: number;
    requestId?: string;
  }): Promise<{ jobId: string; requestId: string }> {
    try {
      const requestId = options?.requestId || `daily-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      const jobData: SymbolDownloadJobDataType = {
        exchange: 'ALL',
        segment: 'ALL',
        forceRefresh: options?.forceRefresh || false,
        batchSize: options?.batchSize || SymbolConstants.DefaultBatchSize,
        requestId,
        scheduledAt: this.dateTimeUtils.getUtcNow(),
        priority: options?.priority ? QUEUE_PRIORITY_MAP[options.priority] : 5,
      };

      // Validate job data
      const validatedData = SymbolDownloadJobDataSchema.parse(jobData);

      const jobName = `download-symbols-all-${requestId}`;

      this.logger.log('Adding daily symbol download job', {
        requestId,
        forceRefresh: validatedData.forceRefresh,
        batchSize: validatedData.batchSize,
      });

      const job = options?.priority
        ? await this.addJobWithPriority(jobName, validatedData, options.priority, {
            delay: options.delay,
            removeOnComplete: 50,
            removeOnFail: 20,
          })
        : await this.addJob(jobName, validatedData, {
            delay: options?.delay,
            removeOnComplete: 50,
            removeOnFail: 20,
          });

      const jobId = job.id || 'unknown';

      this.logger.log('Daily symbol download job added successfully', {
        jobId,
        requestId,
      });

      return { jobId, requestId };
    } catch (error) {
      this.logger.error('Failed to add daily symbol download job', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Trigger on-demand symbol master download
   * Useful for manual refreshes or testing outside of scheduled cron jobs
   */
  async triggerOnDemandDownload(options?: {
    exchange?: 'NSE' | 'BSE' | 'NFO' | 'BFO' | 'CDS' | 'MCX' | 'ALL';
    segment?: 'NSE' | 'BSE' | 'CDS' | 'MCX' | 'ALL' | 'NFO-FUT' | 'NFO-OPT' | 'BFO-FUT' | 'BFO-OPT';
    forceRefresh?: boolean;
    batchSize?: number;
    priority?: QueuePriorityType;
  }): Promise<{ jobId: string; requestId: string }> {
    try {
      const requestId = `ondemand-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      const jobData: SymbolDownloadJobDataType = {
        exchange: options?.exchange || 'ALL',
        segment: options?.segment || 'ALL',
        forceRefresh: options?.forceRefresh ?? true, // Default to true for on-demand downloads
        batchSize: options?.batchSize || SymbolConstants.DefaultBatchSize,
        requestId,
        scheduledAt: this.dateTimeUtils.getUtcNow(),
        priority: options?.priority ? QUEUE_PRIORITY_MAP[options.priority] : 1, // High priority for on-demand
      };

      // Validate job data
      const validatedData = SymbolDownloadJobDataSchema.parse(jobData);

      const jobName = `download-symbols-ondemand-${requestId}`;

      this.logger.log('Triggering on-demand symbol download', {
        requestId,
        exchange: validatedData.exchange,
        segment: validatedData.segment,
        forceRefresh: validatedData.forceRefresh,
        batchSize: validatedData.batchSize,
      });

      // Always use high priority for on-demand downloads
      const job = await this.addJobWithPriority(jobName, validatedData, options?.priority || 'HIGH', {
        removeOnComplete: 50,
        removeOnFail: 20,
      });

      const jobId = job.id || 'unknown';

      this.logger.log('On-demand symbol download job added successfully', {
        jobId,
        requestId,
      });

      return { jobId, requestId };
    } catch (error) {
      this.logger.error('Failed to trigger on-demand symbol download', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== SYMBOL-SPECIFIC JOB STATUS AND MONITORING ====================

  /**
   * Get queue statistics and health information
   */
  async getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: number;
    cronJobStatus: 'active' | 'inactive' | 'unknown';
    lastCronRun?: Date;
    nextCronRun?: Date;
    totalProcessed: number;
    avgProcessingTime?: number;
  }> {
    try {
      const healthStatus = await this.getHealthStatus();

      // Check cron job status
      let cronJobStatus: 'active' | 'inactive' | 'unknown' = 'unknown';
      let lastCronRun: Date | undefined;
      let nextCronRun: Date | undefined;

      try {
        // Try to get the repeating job using the queue's getJob method
        const cronJob = await this.getJob(this.cronJobId);
        if (cronJob && cronJob.opts?.repeat) {
          cronJobStatus = 'active';
          // Note: BullMQ RepeatOptions doesn't expose next run time directly
          // This would need to be calculated based on the cron pattern
        } else {
          cronJobStatus = 'inactive';
        }
      } catch (error) {
        this.logger.warn('Failed to get cron job status', {
          error: this.errorUtils.getErrorMessage(error),
        });
      }

      return {
        waiting: healthStatus.waiting,
        active: healthStatus.active,
        completed: healthStatus.completed,
        failed: healthStatus.failed,
        delayed: healthStatus.delayed,
        paused: healthStatus.paused ? 1 : 0,
        cronJobStatus,
        lastCronRun,
        nextCronRun,
        totalProcessed: healthStatus.completed + healthStatus.failed,
        avgProcessingTime: undefined, // Could be calculated from job history
      };
    } catch (error) {
      this.logger.error('Failed to get queue statistics', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== SYMBOL-SPECIFIC JOB MANAGEMENT ====================

  /**
   * Get comprehensive queue health status
   */
  async getComprehensiveHealthStatus(): Promise<{
    isHealthy: boolean;
    queueStats: {
      waiting: number;
      active: number;
      completed: number;
      failed: number;
      delayed: number;
      paused: number;
      cronJobStatus: 'active' | 'inactive' | 'unknown';
      lastCronRun?: Date;
      nextCronRun?: Date;
      totalProcessed: number;
      avgProcessingTime?: number;
    } | null;
    cronJobStatus: string;
    lastActivity?: Date;
    issues: string[];
  }> {
    try {
      const queueStats = await this.getQueueStats();
      const issues: string[] = [];

      // Check for issues
      if (queueStats.cronJobStatus === 'inactive') {
        issues.push('Daily cron job is not active');
      }

      if (queueStats.failed > queueStats.completed * 0.1) {
        issues.push('High failure rate detected');
      }

      if (queueStats.waiting > 100) {
        issues.push('Large number of waiting jobs');
      }

      const isHealthy = issues.length === 0;

      return {
        isHealthy,
        queueStats,
        cronJobStatus: queueStats.cronJobStatus,
        lastActivity: queueStats.nextCronRun,
        issues,
      };
    } catch (error) {
      this.logger.error('Failed to get comprehensive health status', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        isHealthy: false,
        queueStats: null,
        cronJobStatus: 'unknown',
        issues: ['Failed to get health status'],
      };
    }
  }
}
