import { z } from 'zod/v4';
import {
  baseUpdatableEntitySchema,
  nameSchema,
  optionalUtcDateTimeSchema,
  positiveIntSchema,
  nonNegativeIntSchema,
} from '@app/common/schema';
import { BrokerTypeEnum, BrokerStatusEnum, BrokerConnectionStatusEnum, BROKER_VALIDATION } from './broker.constants';

// ==================== BROKER VALIDATION SCHEMAS ====================

/**
 * API Key validation schema for Zerodha Kite
 */
export const apiKeySchema = z
  .string()
  .length(BROKER_VALIDATION.API_KEY_LENGTH, `API key must be exactly ${BROKER_VALIDATION.API_KEY_LENGTH} characters`)
  .regex(BROKER_VALIDATION.API_KEY_PATTERN, 'API key must contain only lowercase letters and numbers')
  .describe('Broker API key for authentication');

/**
 * API Secret validation schema for Zerodha Kite
 */
export const apiSecretSchema = z
  .string()
  .length(
    BROKER_VALIDATION.API_SECRET_LENGTH,
    `API secret must be exactly ${BROKER_VALIDATION.API_SECRET_LENGTH} characters`,
  )
  .regex(BROKER_VALIDATION.API_SECRET_PATTERN, 'API secret must contain only lowercase letters and numbers')
  .describe('Broker API secret for authentication');

/**
 * Access Token validation schema
 */
export const accessTokenSchema = z
  .string()
  .min(
    BROKER_VALIDATION.ACCESS_TOKEN_MIN_LENGTH,
    `Access token must be at least ${BROKER_VALIDATION.ACCESS_TOKEN_MIN_LENGTH} characters`,
  )
  .max(
    BROKER_VALIDATION.ACCESS_TOKEN_MAX_LENGTH,
    `Access token cannot exceed ${BROKER_VALIDATION.ACCESS_TOKEN_MAX_LENGTH} characters`,
  )
  .regex(BROKER_VALIDATION.ACCESS_TOKEN_PATTERN, 'Access token contains invalid characters')
  .describe('Broker access token for API calls');

/**
 * Optional access token schema
 */
export const optionalAccessTokenSchema = accessTokenSchema.optional();

/**
 * User ID schema for broker association
 */
export const userIdSchema = z
  .string()
  .min(1, 'User ID is required')
  .max(255, 'User ID cannot exceed 255 characters')
  .describe('User ID associated with broker credentials');

/**
 * Broker settings schema for configuration
 */
export const brokerSettingsSchema = z
  .object({
    autoReconnect: z.boolean().default(true).describe('Enable automatic reconnection'),
    maxRetryAttempts: nonNegativeIntSchema.max(10).default(3).describe('Maximum retry attempts'),
    retryDelay: positiveIntSchema.max(60000).default(1000).describe('Retry delay in milliseconds'),
    subscriptionMode: z.enum(['ltp', 'quote', 'full']).default('quote').describe('WebSocket subscription mode'),
    enableOrderUpdates: z.boolean().default(true).describe('Enable order update notifications'),
    enablePositionUpdates: z.boolean().default(true).describe('Enable position update notifications'),
  })
  .strict()
  .describe('Broker-specific configuration settings');

// ==================== CORE BROKER SCHEMAS ====================

/**
 * Complete Broker entity schema with all fields
 * Extends base updatable entity with audit fields
 */
export const BrokerSchema = z.object({
  ...baseUpdatableEntitySchema.shape,
  name: nameSchema
    .min(BROKER_VALIDATION.NAME_MIN_LENGTH)
    .max(BROKER_VALIDATION.NAME_MAX_LENGTH)
    .describe('Human-readable name for the broker configuration'),
  type: BrokerTypeEnum.describe('Type of broker (e.g., ZERODHA_KITE)'),
  userId: userIdSchema.describe('ID of the user who owns this broker configuration'),
  apiKey: apiKeySchema.describe('Broker API key for authentication'),
  apiSecret: apiSecretSchema.describe('Broker API secret for authentication'),
  accessToken: optionalAccessTokenSchema.describe('Broker access token (obtained after login)'),
  status: BrokerStatusEnum.default('INACTIVE').describe('Current status of broker credentials'),
  isActive: z.boolean().default(true).describe('Whether broker configuration is active'),
  lastConnectedAt: optionalUtcDateTimeSchema.describe('Last successful connection timestamp'),
  lastErrorMessage: z.string().max(1000).optional().describe('Last error message from broker'),
  connectionAttempts: nonNegativeIntSchema.default(0).describe('Number of connection attempts'),
  settings: brokerSettingsSchema.optional().describe('Broker-specific configuration settings'),
});

/**
 * Schema for creating a new broker configuration
 * Excludes system-generated fields and userId (retrieved from authentication context)
 */
export const CreateBrokerSchema = z
  .object({
    name: nameSchema
      .min(BROKER_VALIDATION.NAME_MIN_LENGTH)
      .max(BROKER_VALIDATION.NAME_MAX_LENGTH)
      .describe('Human-readable name for the broker configuration'),
    type: BrokerTypeEnum.describe('Type of broker (e.g., ZERODHA_KITE)'),
    apiKey: apiKeySchema.describe('Broker API key for authentication'),
    apiSecret: apiSecretSchema.describe('Broker API secret for authentication'),
    accessToken: optionalAccessTokenSchema.describe('Broker access token (optional during creation)'),
    settings: brokerSettingsSchema.optional().describe('Broker-specific configuration settings'),
  })
  .strict();

/**
 * Schema for updating an existing broker configuration
 * All fields are optional except those that shouldn't change
 */
export const UpdateBrokerSchema = z
  .object({
    name: nameSchema
      .min(BROKER_VALIDATION.NAME_MIN_LENGTH)
      .max(BROKER_VALIDATION.NAME_MAX_LENGTH)
      .optional()
      .describe('Human-readable name for the broker configuration'),
    apiKey: apiKeySchema.optional().describe('Updated broker API key'),
    apiSecret: apiSecretSchema.optional().describe('Updated broker API secret'),
    accessToken: optionalAccessTokenSchema.describe('Updated broker access token'),
    status: BrokerStatusEnum.optional().describe('Updated broker status'),
    isActive: z.boolean().optional().describe('Whether broker configuration is active'),
    lastErrorMessage: z.string().max(1000).optional().describe('Last error message from broker'),
    settings: brokerSettingsSchema.optional().describe('Updated broker-specific settings'),
  })
  .strict();

/**
 * Schema for broker search and filtering
 */
export const BrokerSearchSchema = z
  .object({
    userId: userIdSchema.optional().describe('Filter by user ID'),
    type: BrokerTypeEnum.optional().describe('Filter by broker type'),
    status: BrokerStatusEnum.optional().describe('Filter by broker status'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    hasAccessToken: z.boolean().optional().describe('Filter by presence of access token'),
    name: z.string().min(1).max(100).optional().describe('Filter by broker name (partial match)'),
  })
  .strict();

// ==================== RESPONSE SCHEMAS ====================

/**
 * Public broker schema (excludes sensitive fields)
 * Used for API responses to avoid exposing credentials
 */
export const PublicBrokerSchema = z.object({
  ...baseUpdatableEntitySchema.shape,
  name: nameSchema.describe('Human-readable name for the broker configuration'),
  type: BrokerTypeEnum.describe('Type of broker'),
  userId: userIdSchema.describe('ID of the user who owns this broker configuration'),
  status: BrokerStatusEnum.describe('Current status of broker credentials'),
  isActive: z.boolean().describe('Whether broker configuration is active'),
  lastConnectedAt: optionalUtcDateTimeSchema.describe('Last successful connection timestamp'),
  lastErrorMessage: z.string().optional().describe('Last error message from broker'),
  connectionAttempts: nonNegativeIntSchema.describe('Number of connection attempts'),
  hasAccessToken: z.boolean().describe('Whether broker has a valid access token'),
  settings: brokerSettingsSchema.optional().describe('Broker-specific configuration settings'),
});

/**
 * Broker connection status schema for real-time updates
 */
export const BrokerConnectionSchema = z.object({
  brokerId: positiveIntSchema.describe('Broker configuration ID'),
  status: BrokerConnectionStatusEnum.describe('Current connection status'),
  lastUpdated: optionalUtcDateTimeSchema.describe('Last status update timestamp'),
  errorMessage: z.string().optional().describe('Error message if connection failed'),
});

// ==================== OPERATION SCHEMAS ====================

/**
 * Schema for broker credential validation
 */
export const ValidateBrokerCredentialsSchema = z
  .object({
    type: BrokerTypeEnum.describe('Type of broker to validate'),
    apiKey: apiKeySchema.describe('API key to validate'),
    apiSecret: apiSecretSchema.describe('API secret to validate'),
    accessToken: optionalAccessTokenSchema.describe('Access token to validate (optional)'),
  })
  .strict();

/**
 * Schema for broker authentication result
 */
export const BrokerAuthResultSchema = z.object({
  success: z.boolean().describe('Whether authentication was successful'),
  accessToken: accessTokenSchema.optional().describe('Generated access token if successful'),
  expiresAt: optionalUtcDateTimeSchema.describe('Token expiration timestamp'),
  errorMessage: z.string().optional().describe('Error message if authentication failed'),
  userProfile: z.record(z.string(), z.unknown()).optional().describe('User profile data from broker'),
});

// ==================== TYPE EXPORTS ====================
// Following PatternTrade API standards: Export TypeScript types from Zod schemas

export type Broker = z.output<typeof BrokerSchema>;
export type CreateBroker = z.output<typeof CreateBrokerSchema>;
export type UpdateBroker = z.output<typeof UpdateBrokerSchema>;
export type BrokerSearch = z.output<typeof BrokerSearchSchema>;
export type PublicBroker = z.output<typeof PublicBrokerSchema>;
export type BrokerConnection = z.output<typeof BrokerConnectionSchema>;
export type ValidateBrokerCredentials = z.output<typeof ValidateBrokerCredentialsSchema>;
export type BrokerAuthResult = z.output<typeof BrokerAuthResultSchema>;
export type BrokerSettings = z.output<typeof brokerSettingsSchema>;

// Authentication types
export type GenerateLoginUrlRequest = z.output<typeof GenerateLoginUrlSchema>;
export type GenerateAccessTokenRequest = z.output<typeof GenerateAccessTokenSchema>;
export type ValidateSessionRequest = z.output<typeof ValidateSessionSchema>;
export type LoginUrlResponse = z.output<typeof LoginUrlResponseSchema>;
export type AccessTokenResponse = z.output<typeof AccessTokenResponseSchema>;
export type SessionValidationResponse = z.output<typeof SessionValidationResponseSchema>;
export type GetBrokerCredentialsRequest = z.output<typeof GetBrokerCredentialsSchema>;
export type BrokerCredentialsResponse = z.output<typeof BrokerCredentialsResponseSchema>;
export type StoredTokenResponse = z.output<typeof StoredTokenResponseSchema>;

// ==================== AUTHENTICATION SCHEMAS ====================

/**
 * Schema for generating Kite Connect login URL
 */
export const GenerateLoginUrlSchema = z
  .object({
    redirectUrl: z.url().optional().describe('Optional redirect URL after login'),
  })
  .strict();

/**
 * Schema for exchanging request token for access token (with database storage)
 */
export const GenerateAccessTokenSchema = z
  .object({
    requestToken: z.string().min(1).max(255).describe('Request token from Kite Connect login'),
  })
  .strict();

/**
 * Schema for validating existing session (database-first approach)
 */
export const ValidateSessionSchema = z
  .object({
    apiKey: apiKeySchema.optional().describe('Zerodha Kite API key (optional if stored in DB)'),
    accessToken: accessTokenSchema.optional().describe('Zerodha Kite access token (optional if stored in DB)'),
  })
  .strict();

/**
 * Schema for login URL response
 */
export const LoginUrlResponseSchema = z
  .object({
    loginUrl: z.string().url().describe('Kite Connect login URL'),
    apiKey: apiKeySchema.describe('API key used for login'),
    expiresAt: z.string().datetime().describe('URL expiration timestamp'),
  })
  .strict();

/**
 * Schema for access token response
 */
export const AccessTokenResponseSchema = z
  .object({
    accessToken: accessTokenSchema.describe('Generated access token'),
    userId: z.string().min(1).describe('Kite user ID'),
    userName: z.string().min(1).describe('Kite user name'),
    email: z.string().email().optional().describe('User email if available'),
    broker: z.literal('ZERODHA_KITE').describe('Broker type'),
    loginTime: z.string().datetime().describe('Login timestamp'),
    expiresAt: z.string().datetime().describe('Token expiration timestamp'),
  })
  .strict();

/**
 * Schema for session validation response
 */
export const SessionValidationResponseSchema = z
  .object({
    isValid: z.boolean().describe('Whether session is valid'),
    userId: z.string().min(1).optional().describe('Kite user ID if valid'),
    userName: z.string().min(1).optional().describe('Kite user name if valid'),
    email: z.string().email().optional().describe('User email if available'),
    loginTime: z.string().datetime().optional().describe('Login timestamp if valid'),
    expiresAt: z.string().datetime().optional().describe('Token expiration if valid'),
  })
  .strict();

/**
 * Schema for retrieving broker credentials from database
 */
export const GetBrokerCredentialsSchema = z
  .object({
    brokerType: BrokerTypeEnum.optional().default('ZERODHA_KITE').describe('Broker type (defaults to Zerodha Kite)'),
  })
  .strict();

/**
 * Schema for broker credentials response
 */
export const BrokerCredentialsResponseSchema = z
  .object({
    apiKey: apiKeySchema.describe('Broker API key'),
    apiSecret: apiSecretSchema.describe('Broker API secret'),
    brokerType: BrokerTypeEnum.describe('Broker type'),
    hasAccessToken: z.boolean().describe('Whether access token is available'),
  })
  .strict();

/**
 * Schema for stored token response
 */
export const StoredTokenResponseSchema = z
  .object({
    accessToken: accessTokenSchema.describe('Stored access token'),
    brokerUserId: z.string().min(1).describe('Broker user ID'),
    brokerUserName: z.string().min(1).describe('Broker user name'),
    email: z.string().email().optional().describe('User email if available'),
    loginTime: z.string().datetime().describe('Login timestamp'),
    expiresAt: z.string().datetime().describe('Token expiration timestamp'),
    isValid: z.boolean().describe('Whether token is still valid'),
  })
  .strict();

// ==================== SCHEMA COLLECTIONS ====================

/**
 * Collection of all broker-related schemas for easy access
 */
export const BrokerSchemas = {
  // Core schemas
  Broker: BrokerSchema,
  CreateBroker: CreateBrokerSchema,
  UpdateBroker: UpdateBrokerSchema,
  BrokerSearch: BrokerSearchSchema,

  // Response schemas
  PublicBroker: PublicBrokerSchema,
  BrokerConnection: BrokerConnectionSchema,

  // Operation schemas
  ValidateBrokerCredentials: ValidateBrokerCredentialsSchema,
  BrokerAuthResult: BrokerAuthResultSchema,

  // Authentication schemas
  GenerateLoginUrl: GenerateLoginUrlSchema,
  GenerateAccessToken: GenerateAccessTokenSchema,
  ValidateSession: ValidateSessionSchema,
  LoginUrlResponse: LoginUrlResponseSchema,
  AccessTokenResponse: AccessTokenResponseSchema,
  SessionValidationResponse: SessionValidationResponseSchema,
  GetBrokerCredentials: GetBrokerCredentialsSchema,
  BrokerCredentialsResponse: BrokerCredentialsResponseSchema,
  StoredTokenResponse: StoredTokenResponseSchema,

  // Component schemas
  BrokerSettings: brokerSettingsSchema,
  ApiKey: apiKeySchema,
  ApiSecret: apiSecretSchema,
  AccessToken: accessTokenSchema,
} as const;
