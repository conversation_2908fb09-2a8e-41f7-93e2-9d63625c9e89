import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import { PaginationOptions, PaginatedResult } from '@app/common/repository';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { BrokerRepository } from './broker.repository';
import { CreateBroker, UpdateBroker, BrokerSearch, PublicBroker } from './broker.schema';
import { BrokerType, BrokerStatus, validateBrokerCredentials } from './broker.constants';
import { BrokerNotFoundError, InvalidCredentialsError, createBrokerError } from './broker.error';
import { BrokerTableSelect } from './broker.model';

/**
 * Broker service for business logic operations
 * Handles broker credential management and validation
 */
@Injectable()
export class BrokerService {
  private readonly logger = new Logger(BrokerService.name);

  constructor(
    private readonly brokerRepository: BrokerRepository,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
    private readonly clsService: ClsService,
  ) {}

  // ==================== HELPER METHODS ====================

  /**
   * Get authenticated user information from CLS session
   * @returns User information from session
   * @throws UnauthorizedException if session or user not found
   */
  private getAuthenticatedUser(): { id: string; email: string; role: string } {
    try {
      const session = this.clsService.get('session');

      if (!session || !session.user) {
        this.logger.error('No session or user found in CLS context');
        throw new UnauthorizedException('User authentication required');
      }

      const user = session.user;
      if (!user.id || !user.email) {
        this.logger.error('Incomplete user information in session');
        throw new UnauthorizedException('Invalid user session data');
      }

      return {
        id: user.id,
        email: user.email,
        role: user.role || 'user',
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error('Failed to retrieve user from CLS session:', error);
      throw new UnauthorizedException('Failed to retrieve user session');
    }
  }

  // ==================== CRUD OPERATIONS ====================

  /**
   * Create a new broker configuration
   */
  async createBroker(data: CreateBroker): Promise<PublicBroker> {
    try {
      // Get authenticated user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Creating broker configuration: ${data.name} (${data.type}) for user ${user.id}`);

      // Validate broker type is supported
      if (!this.isSupportedBrokerType(data.type)) {
        throw createBrokerError('UNSUPPORTED_BROKER_TYPE', { brokerType: data.type });
      }

      // Check if user already has a broker of this type
      const existingBroker = await this.brokerRepository.findByUserIdAndType(user.id, data.type);
      if (existingBroker) {
        throw createBrokerError('BROKER_CREATION_FAILED', {
          userId: user.id,
          brokerType: data.type,
          reason: 'User already has a broker of this type',
        });
      }

      // Validate credentials format
      if (
        !validateBrokerCredentials(data.type, {
          apiKey: data.apiKey,
          apiSecret: data.apiSecret,
          accessToken: data.accessToken,
        })
      ) {
        throw new InvalidCredentialsError(data.type, 'Invalid credential format');
      }

      // Create broker with user ID from authentication context
      const brokerData = {
        name: data.name,
        type: data.type,
        userId: user.id,
        apiKey: data.apiKey,
        apiSecret: data.apiSecret,
        accessToken: data.accessToken,
        settings: data.settings,
      };

      const createdBroker = await this.brokerRepository.create(brokerData);

      this.logger.log(`Successfully created broker with ID: ${createdBroker.id}`);
      return this.toPublicBroker(createdBroker);
    } catch (error) {
      this.logger.error('Failed to create broker configuration', {
        data: { ...data, apiSecret: '[REDACTED]', accessToken: '[REDACTED]' },
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Update an existing broker configuration
   */
  async updateBroker(id: number, data: UpdateBroker): Promise<PublicBroker> {
    try {
      // Get authenticated user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Updating broker configuration ${id} for user ${user.id}`);

      // Verify broker exists and belongs to user
      const existingBroker = await this.brokerRepository.findByIdAndUserId(id, user.id);
      if (!existingBroker) {
        throw new BrokerNotFoundError(id, user.id);
      }

      // Validate credentials if provided
      if (data.apiKey || data.apiSecret) {
        const credentials = {
          apiKey: data.apiKey || existingBroker.apiKey,
          apiSecret: data.apiSecret || existingBroker.apiSecret,
          accessToken: data.accessToken || existingBroker.accessToken || undefined,
        };

        if (!validateBrokerCredentials(existingBroker.type as BrokerType, credentials)) {
          throw new InvalidCredentialsError(existingBroker.type, 'Invalid credential format');
        }
      }

      const updatedBroker = await this.brokerRepository.update(id, data);

      this.logger.log(`Successfully updated broker with ID: ${id}`);
      return this.toPublicBroker(updatedBroker);
    } catch (error) {
      // Get user ID for error logging (may be undefined if getAuthenticatedUser failed)
      let userId: string | undefined;
      try {
        userId = this.getAuthenticatedUser().id;
      } catch {
        userId = 'unknown';
      }

      this.logger.error('Failed to update broker configuration', {
        id,
        userId,
        data: { ...data, apiSecret: '[REDACTED]', accessToken: '[REDACTED]' },
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Delete a broker configuration (soft delete)
   */
  async deleteBroker(id: number): Promise<{ message: string }> {
    try {
      // Get authenticated user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Deleting broker configuration ${id} for user ${user.id}`);

      // Verify broker exists and belongs to user
      const existingBroker = await this.brokerRepository.findByIdAndUserId(id, user.id);
      if (!existingBroker) {
        throw new BrokerNotFoundError(id, user.id);
      }

      await this.brokerRepository.delete(id);

      this.logger.log(`Successfully deleted broker with ID: ${id}`);
      return { message: `Broker configuration with ID ${id} has been deleted` };
    } catch (error) {
      // Get user ID for error logging (may be undefined if getAuthenticatedUser failed)
      let userId: string | undefined;
      try {
        userId = this.getAuthenticatedUser().id;
      } catch {
        userId = 'unknown';
      }

      this.logger.error('Failed to delete broker configuration', {
        id,
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get broker by ID for a specific user
   */
  async findBrokerById(id: number): Promise<PublicBroker> {
    try {
      // Get authenticated user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Finding broker ${id} for user ${user.id}`);

      const broker = await this.brokerRepository.findByIdAndUserId(id, user.id);
      if (!broker) {
        throw new BrokerNotFoundError(id, user.id);
      }

      return this.toPublicBroker(broker);
    } catch (error) {
      // Get user ID for error logging (may be undefined if getAuthenticatedUser failed)
      let userId: string | undefined;
      try {
        userId = this.getAuthenticatedUser().id;
      } catch {
        userId = 'unknown';
      }

      this.logger.error('Failed to find broker', {
        id,
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get all brokers for a user with pagination
   */
  async findAllBrokers(filters: BrokerSearch, options?: PaginationOptions): Promise<PaginatedResult<PublicBroker>> {
    try {
      this.logger.log('Finding brokers with filters', { filters, options });

      const result = await this.brokerRepository.search(filters, options);

      return {
        ...result,
        data: result.data.map((broker) => this.toPublicBroker(broker)),
      };
    } catch (error) {
      this.logger.error('Failed to find brokers', {
        filters,
        options,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Update broker credentials with validation
   */
  async updateCredentials(
    id: number,
    credentials: {
      apiKey?: string;
      apiSecret?: string;
      accessToken?: string;
      status?: BrokerStatus;
    },
  ): Promise<PublicBroker> {
    try {
      // Get authenticated user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Updating credentials for broker ${id}`);

      const updatedBroker = await this.brokerRepository.updateCredentials(id, user.id, credentials);

      this.logger.log(`Successfully updated credentials for broker ${id}`);
      return this.toPublicBroker(updatedBroker);
    } catch (error) {
      // Get user ID for error logging (may be undefined if getAuthenticatedUser failed)
      let userId: string | undefined;
      try {
        userId = this.getAuthenticatedUser().id;
      } catch {
        userId = 'unknown';
      }

      this.logger.error('Failed to update broker credentials', {
        id,
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get active brokers for the authenticated user
   */
  async getActiveBrokers(options?: PaginationOptions): Promise<PaginatedResult<PublicBroker>> {
    try {
      // Get authenticated user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Getting active brokers for user ${user.id}`);

      const result = await this.brokerRepository.findActiveByUserId(user.id, options);

      return {
        ...result,
        data: result.data.map((broker) => this.toPublicBroker(broker)),
      };
    } catch (error) {
      // Get user ID for error logging (may be undefined if getAuthenticatedUser failed)
      let userId: string | undefined;
      try {
        userId = this.getAuthenticatedUser().id;
      } catch {
        userId = 'unknown';
      }

      this.logger.error('Failed to get active brokers', {
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get active brokers for a specific user (admin use case)
   */
  async getActiveBrokersForUser(userId: string, options?: PaginationOptions): Promise<PaginatedResult<PublicBroker>> {
    try {
      this.logger.log(`Getting active brokers for user ${userId}`);

      const result = await this.brokerRepository.findActiveByUserId(userId, options);

      return {
        ...result,
        data: result.data.map((broker) => this.toPublicBroker(broker)),
      };
    } catch (error) {
      this.logger.error('Failed to get active brokers', {
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get broker status counts for the authenticated user
   */
  async getBrokerStatusCounts(): Promise<Record<BrokerStatus, number>> {
    try {
      // Get authenticated user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Getting broker status counts for user ${user.id}`);

      const counts = await this.brokerRepository.countByUserAndStatus(user.id);

      this.logger.log(`Retrieved broker status counts for user ${user.id}`, counts);
      return counts;
    } catch (error) {
      // Get user ID for error logging (may be undefined if getAuthenticatedUser failed)
      let userId: string | undefined;
      try {
        userId = this.getAuthenticatedUser().id;
      } catch {
        userId = 'unknown';
      }

      this.logger.error('Failed to get broker status counts', {
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get broker status counts for a specific user (admin use case)
   */
  async getBrokerStatusCountsForUser(userId: string): Promise<Record<BrokerStatus, number>> {
    try {
      this.logger.log(`Getting broker status counts for user ${userId}`);

      const counts = await this.brokerRepository.countByUserAndStatus(userId);

      this.logger.log(`Retrieved broker status counts for user ${userId}`, counts);
      return counts;
    } catch (error) {
      this.logger.error('Failed to get broker status counts', {
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== HELPER METHODS ====================

  /**
   * Check if broker type is supported
   */
  private isSupportedBrokerType(type: string): type is BrokerType {
    return type === 'ZERODHA_KITE';
  }

  /**
   * Get default settings for broker type
   */
  private getDefaultSettings(type: BrokerType): Record<string, unknown> {
    switch (type) {
      case 'ZERODHA_KITE':
        return {
          autoReconnect: true,
          maxRetryAttempts: 3,
          retryDelay: 1000,
          subscriptionMode: 'quote',
          enableOrderUpdates: true,
          enablePositionUpdates: true,
        };
      default:
        return {};
    }
  }

  /**
   * Convert broker entity to public broker (excluding sensitive fields)
   */
  private toPublicBroker(broker: BrokerTableSelect): PublicBroker {
    return {
      id: broker.id,
      name: broker.name,
      type: broker.type as BrokerType,
      userId: broker.userId,
      status: broker.status as BrokerStatus,
      isActive: broker.isActive,
      lastConnectedAt: broker.lastConnectedAt || undefined,
      lastErrorMessage: broker.lastErrorMessage || undefined,
      connectionAttempts: parseInt(broker.connectionAttempts || '0', 10),
      hasAccessToken: !!broker.accessToken,
      settings: broker.settings ? JSON.parse(broker.settings) : undefined,
      createdAt: broker.createdAt,
      createdBy: broker.createdBy,
      updatedAt: broker.updatedAt,
      updatedBy: broker.updatedBy,
      deletedAt: broker.deletedAt || undefined,
      deletedBy: broker.deletedBy || undefined,
    };
  }
}
