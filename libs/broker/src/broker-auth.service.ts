import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import { KiteConnect } from 'kiteconnect';
import { DateTimeUtilsService } from '@app/utils';
import { EncryptionService } from '@app/common/encryption';
import { z } from 'zod/v4';
import { createKiteBrokerError, InvalidCredentialsError, TokenExpiredError } from './broker.error';
import {
  GenerateLoginUrlSchema,
  GenerateAccessTokenSchema,
  ValidateSessionSchema,
  LoginUrlResponseSchema,
  AccessTokenResponseSchema,
  SessionValidationResponseSchema,
  GetBrokerCredentialsSchema,
  BrokerCredentialsResponseSchema,
  StoredTokenResponseSchema,
} from './broker.schema';
import type {
  GenerateLoginUrlRequest,
  GenerateAccessTokenRequest,
  ValidateSessionRequest,
  LoginUrlResponse,
  AccessTokenResponse,
  SessionValidationResponse,
  GetBrokerCredentialsRequest,
  BrokerCredentialsResponse,
  StoredTokenResponse,
} from './broker.schema';
import { BrokerRepository } from './broker.repository';

// ==================== BROKER AUTHENTICATION SERVICE ====================

/**
 * Broker Authentication Service for Zerodha Kite Connect
 *
 * Provides authentication methods for Zerodha Kite Connect integration:
 * - Generate login URLs for user authentication
 * - Exchange request tokens for access tokens
 * - Validate existing sessions and tokens
 * - Handle Kite-specific authentication flows
 *
 * Features:
 * - Comprehensive error handling with domain-specific errors
 * - Structured logging for authentication events
 * - Zod schema validation for all inputs/outputs
 * - Session management integration
 * - Token expiration handling
 */
@Injectable()
export class BrokerAuthService {
  private readonly logger = new Logger(BrokerAuthService.name);

  constructor(
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly brokerRepository: BrokerRepository,
    private readonly encryptionService: EncryptionService,
    private readonly clsService: ClsService,
  ) {}

  // ==================== HELPER METHODS ====================

  /**
   * Get authenticated user information from CLS session
   * @returns User information from session
   * @throws UnauthorizedException if session or user not found
   */
  private getAuthenticatedUser(): { id: string; email: string; role: string } {
    try {
      const session = this.clsService.get('session');

      if (!session || !session.user) {
        this.logger.error('No session or user found in CLS context');
        throw new UnauthorizedException('User authentication required');
      }

      const user = session.user;
      if (!user.id || !user.email) {
        this.logger.error('Incomplete user information in session');
        throw new UnauthorizedException('Invalid user session data');
      }

      return {
        id: user.id,
        email: user.email,
        role: user.role || 'user',
      };
    } catch (error) {
      this.logger.error(
        `Failed to get authenticated user: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error instanceof UnauthorizedException ? error : new UnauthorizedException('Authentication required');
    }
  }

  // ==================== DATABASE INTEGRATION METHODS ====================

  /**
   * Retrieve broker credentials from database
   *
   * @param request - Broker credentials retrieval request
   * @returns Promise<BrokerCredentialsResponse> - Broker credentials with metadata
   * @throws BrokerError if credentials not found or decryption fails
   */
  async getBrokerCredentials(request: GetBrokerCredentialsRequest): Promise<BrokerCredentialsResponse> {
    try {
      // Get current user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Retrieving broker credentials for user: ${user.id}, type: ${request.brokerType}`);

      // Validate input
      const validatedRequest = GetBrokerCredentialsSchema.parse(request);

      // Find broker by user ID and type
      const broker = await this.brokerRepository.findByUserIdAndType(user.id, validatedRequest.brokerType);

      if (!broker) {
        throw createKiteBrokerError(
          'BROKER_NOT_FOUND',
          undefined,
          `No ${validatedRequest.brokerType} broker found for user ${user.id}`,
        );
      }

      // Decrypt sensitive fields
      const apiSecret = this.decryptField(broker.apiSecret, 'API secret');

      const response: BrokerCredentialsResponse = {
        apiKey: broker.apiKey,
        apiSecret,
        brokerType: validatedRequest.brokerType,
        hasAccessToken: !!broker.accessToken,
      };

      this.logger.log(`Successfully retrieved broker credentials for user: ${user.id}`);
      return BrokerCredentialsResponseSchema.parse(response);
    } catch (error) {
      this.logger.error(
        `Failed to retrieve broker credentials: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error,
      );

      if (error instanceof z.ZodError) {
        throw new InvalidCredentialsError('ZERODHA_KITE', 'Invalid request parameters for credential retrieval');
      }

      throw error;
    }
  }

  /**
   * Store access token to database after successful authentication
   * @private
   */
  private async storeAccessToken(brokerType: 'ZERODHA_KITE', accessToken: string): Promise<void> {
    try {
      // Get current user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Storing access token for user: ${user.id}, type: ${brokerType}`);

      // Find existing broker
      const broker = await this.brokerRepository.findByUserIdAndType(user.id, brokerType);

      if (!broker) {
        throw createKiteBrokerError('BROKER_NOT_FOUND', undefined, `No ${brokerType} broker found for user ${user.id}`);
      }

      // Encrypt access token
      const encryptedAccessToken = this.encryptField(accessToken, 'Access token');

      // Update broker with access token and metadata
      await this.brokerRepository.updateCredentials(broker.id, user.id, {
        accessToken: encryptedAccessToken,
        status: 'ACTIVE',
      });

      this.logger.log(`Successfully stored access token for user: ${user.id}`);
    } catch (error) {
      this.logger.error(
        `Failed to store access token: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Retrieve stored access token from database
   *
   * @param request - Broker credentials request
   * @returns Promise<StoredTokenResponse | null> - Stored token data or null if not found
   * @throws BrokerError if retrieval fails
   */
  async getStoredAccessToken(request: GetBrokerCredentialsRequest): Promise<StoredTokenResponse | null> {
    try {
      // Get current user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Retrieving stored access token for user: ${user.id}, type: ${request.brokerType}`);

      // Validate input
      const validatedRequest = GetBrokerCredentialsSchema.parse(request);

      // Find broker by user ID and type
      const broker = await this.brokerRepository.findByUserIdAndType(user.id, validatedRequest.brokerType);

      if (!broker || !broker.accessToken) {
        this.logger.warn(`No stored access token found for user: ${user.id}`);
        return null;
      }

      // Decrypt access token
      const accessToken = this.decryptField(broker.accessToken, 'Access token');

      // Check if token is expired (basic check - actual validation should be done via Kite API)
      const now = this.dateTimeUtils.getUtcNow();
      const expiresAt = broker.lastConnectedAt ? this.dateTimeUtils.addHours(broker.lastConnectedAt, 24) : now;

      // Simple string comparison for UTC timestamps (they are in ISO format)
      const isValid = now < expiresAt;

      const response: StoredTokenResponse = {
        accessToken,
        brokerUserId: broker.userId, // This should be the broker's user ID, not our user ID
        brokerUserName: broker.name, // Using broker name as placeholder
        email: undefined, // Not stored in broker table
        loginTime: broker.lastConnectedAt || this.dateTimeUtils.toUtcStorage(now),
        expiresAt: this.dateTimeUtils.toUtcStorage(expiresAt),
        isValid,
      };

      this.logger.log(`Successfully retrieved stored access token for user: ${user.id}`);
      return StoredTokenResponseSchema.parse(response);
    } catch (error) {
      this.logger.error(
        `Failed to retrieve stored access token: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error,
      );

      if (error instanceof z.ZodError) {
        throw new InvalidCredentialsError('ZERODHA_KITE', 'Invalid request parameters for token retrieval');
      }

      throw error;
    }
  }

  // ==================== AUTHENTICATION METHODS ====================

  /**
   * Generate Kite Connect login URL for user authentication
   *
   * @param request - Login URL generation request
   * @returns Promise<LoginUrlResponse> - Generated login URL with metadata
   * @throws BrokerError if URL generation fails
   */
  async generateLoginUrl(request: GenerateLoginUrlRequest): Promise<LoginUrlResponse> {
    try {
      // Get current user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Generating Kite Connect login URL for user: ${user.id}`);

      // Validate input
      GenerateLoginUrlSchema.parse(request);

      // Get broker credentials from database
      const credentials = await this.getBrokerCredentials({ brokerType: 'ZERODHA_KITE' });

      // Create KiteConnect instance
      const kite = new KiteConnect({
        api_key: credentials.apiKey,
      });

      // Generate login URL
      const loginUrl = kite.getLoginURL();
      const expiresAt = this.dateTimeUtils.addHours(this.dateTimeUtils.getUtcNow(), 1); // URLs typically expire in 1 hour

      const response: LoginUrlResponse = {
        loginUrl,
        apiKey: credentials.apiKey,
        expiresAt: this.dateTimeUtils.toUtcStorage(expiresAt),
      };

      this.logger.log(`Successfully generated Kite Connect login URL for user: ${user.id}`);
      return LoginUrlResponseSchema.parse(response);
    } catch (error) {
      this.logger.error(
        `Failed to generate Kite Connect login URL: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error,
      );

      if (error instanceof z.ZodError) {
        throw new InvalidCredentialsError('ZERODHA_KITE', 'Invalid request parameters for login URL generation');
      }

      throw createKiteBrokerError(
        'AUTHENTICATION_FAILED',
        undefined,
        'Failed to generate Kite Connect login URL',
        error,
      );
    }
  }

  /**
   * Generate access token and store it to database for persistent authentication
   *
   * @param request - Access token generation request
   * @returns Promise<AccessTokenResponse> - Generated access token with user info
   * @throws BrokerError if token exchange or storage fails
   */
  async generateAccessToken(request: GenerateAccessTokenRequest): Promise<AccessTokenResponse> {
    try {
      // Get current user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Generating and storing access token for user: ${user.id}`);

      // Validate input
      const validatedRequest = GenerateAccessTokenSchema.parse(request);

      // Get broker credentials from database
      const credentials = await this.getBrokerCredentials({ brokerType: 'ZERODHA_KITE' });

      // Create KiteConnect instance
      const kite = new KiteConnect({
        api_key: credentials.apiKey,
      });

      // Exchange request token for access token
      const sessionData = await kite.generateSession(validatedRequest.requestToken, credentials.apiSecret);

      // Set access token for profile fetch
      kite.setAccessToken(sessionData.access_token);

      // Fetch user profile for additional information
      const profile = await kite.getProfile();

      const loginTime = this.dateTimeUtils.getUtcNow();
      const expiresAt = this.dateTimeUtils.addHours(loginTime, 24); // Kite tokens typically expire in 24 hours

      const response: AccessTokenResponse = {
        accessToken: sessionData.access_token,
        userId: profile.user_id,
        userName: profile.user_name,
        email: profile.email || undefined,
        broker: 'ZERODHA_KITE',
        loginTime: this.dateTimeUtils.toUtcStorage(loginTime),
        expiresAt: this.dateTimeUtils.toUtcStorage(expiresAt),
      };

      // Store the token to database
      await this.storeAccessToken('ZERODHA_KITE', sessionData.access_token);

      this.logger.log(`Successfully generated and stored access token for user: ${user.id}`);
      return AccessTokenResponseSchema.parse(response);
    } catch (error) {
      this.logger.error(
        `Failed to generate and store access token: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error,
      );

      if (error instanceof z.ZodError) {
        throw new InvalidCredentialsError('ZERODHA_KITE', 'Invalid request parameters for access token generation');
      }

      // Handle Kite-specific errors
      if (error && typeof error === 'object' && 'error_type' in error) {
        const kiteError = error as { error_type: string; message: string };

        if (kiteError.error_type === 'TokenException') {
          throw new TokenExpiredError('ZERODHA_KITE');
        }

        if (kiteError.error_type === 'PermissionException') {
          throw new InvalidCredentialsError('ZERODHA_KITE', 'Invalid API credentials');
        }
      }

      throw createKiteBrokerError(
        'AUTHENTICATION_FAILED',
        undefined,
        'Failed to generate and store access token',
        error,
      );
    }
  }

  /**
   * Validate session with database-first approach
   *
   * @param request - Session validation request (credentials optional)
   * @returns Promise<SessionValidationResponse> - Session validation result
   * @throws BrokerError if validation fails
   */
  async validateSession(request: ValidateSessionRequest): Promise<SessionValidationResponse> {
    try {
      // Get current user from CLS
      const user = this.getAuthenticatedUser();

      this.logger.log(`Validating session for user: ${user.id}`);

      // Validate input
      const validatedRequest = ValidateSessionSchema.parse(request);

      // First, try to get stored token from database
      const storedToken = await this.getStoredAccessToken({
        brokerType: 'ZERODHA_KITE',
      });

      // If we have a valid stored token, validate it with Kite API
      if (storedToken && storedToken.isValid) {
        try {
          // Get broker credentials for API key
          const credentials = await this.getBrokerCredentials({
            brokerType: 'ZERODHA_KITE',
          });

          // Create KiteConnect instance and validate
          const kite = new KiteConnect({
            api_key: credentials.apiKey,
          });
          kite.setAccessToken(storedToken.accessToken);

          // Validate by fetching profile
          const profile = await kite.getProfile();

          const response: SessionValidationResponse = {
            isValid: true,
            userId: profile.user_id,
            userName: profile.user_name,
            email: profile.email || undefined,
            loginTime: storedToken.loginTime,
            expiresAt: storedToken.expiresAt,
          };

          this.logger.log(`Session validation successful using stored token for user: ${user.id}`);
          return SessionValidationResponseSchema.parse(response);
        } catch (error) {
          this.logger.warn(
            `Stored token validation failed, will try provided credentials: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }
      }

      // If no stored token or stored token is invalid, try provided credentials
      if (validatedRequest.apiKey && validatedRequest.accessToken) {
        try {
          const kite = new KiteConnect({
            api_key: validatedRequest.apiKey,
          });
          kite.setAccessToken(validatedRequest.accessToken);

          // Validate by fetching profile
          const profile = await kite.getProfile();

          // Store the validated token for future use
          await this.storeAccessToken('ZERODHA_KITE', validatedRequest.accessToken);

          const loginTime = this.dateTimeUtils.getUtcNow();
          const expiresAt = this.dateTimeUtils.addHours(loginTime, 24);

          const response: SessionValidationResponse = {
            isValid: true,
            userId: profile.user_id,
            userName: profile.user_name,
            email: profile.email || undefined,
            loginTime: this.dateTimeUtils.toUtcStorage(loginTime),
            expiresAt: this.dateTimeUtils.toUtcStorage(expiresAt),
          };

          this.logger.log(`Session validation successful using provided credentials for user: ${user.id}`);
          return SessionValidationResponseSchema.parse(response);
        } catch (error) {
          this.logger.warn(
            `Provided credentials validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }
      }

      // If all validation attempts failed
      this.logger.warn(`Session validation failed for user: ${user.id}`);
      return SessionValidationResponseSchema.parse({
        isValid: false,
      });
    } catch (error) {
      this.logger.error(`Session validation error: ${error instanceof Error ? error.message : 'Unknown error'}`, error);

      if (error instanceof z.ZodError) {
        throw new InvalidCredentialsError('ZERODHA_KITE', 'Invalid request parameters for session validation');
      }

      // For other errors, return invalid session
      return SessionValidationResponseSchema.parse({
        isValid: false,
      });
    }
  }

  // ==================== HELPER METHODS ====================

  /**
   * Encrypt sensitive field using EncryptionService
   * @private
   */
  private encryptField(value: string, fieldName: string): string {
    try {
      return this.encryptionService.encrypt(value);
    } catch (error) {
      this.logger.error(`Failed to encrypt ${fieldName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw createKiteBrokerError('AUTHENTICATION_FAILED', undefined, `Failed to encrypt ${fieldName}`, error);
    }
  }

  /**
   * Decrypt sensitive field using EncryptionService
   * @private
   */
  private decryptField(encryptedValue: string, fieldName: string): string {
    try {
      return this.encryptionService.decrypt(encryptedValue);
    } catch (error) {
      this.logger.error(`Failed to decrypt ${fieldName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw createKiteBrokerError('AUTHENTICATION_FAILED', undefined, `Failed to decrypt ${fieldName}`, error);
    }
  }
}
