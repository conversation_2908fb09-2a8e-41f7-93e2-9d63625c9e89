import { Injectable, Logger, OnM<PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { Job } from 'bullmq';
import { DateTimeUtilsService } from '@app/utils';
import { QueueError, type WorkerHealthStatus, type JobResult } from './queue.error';

/**
 * Abstract base class for queue workers using @nestjs/bullmq
 * Provides standardized error handling, job progress tracking, and graceful shutdown
 * Subclasses should use @Processor() decorator and implement process methods
 */
@Injectable()
export abstract class BaseWorkerService<T extends Record<string, unknown> = Record<string, unknown>>
  implements OnModuleInit, OnModuleDestroy
{
  protected readonly logger = new Logger(this.constructor.name);
  protected isRunning = false;
  protected lastProcessedAt?: string;
  protected lastError?: string;
  protected lastErrorAt?: string;

  constructor(protected readonly dateTimeUtils: DateTimeUtilsService) {}

  /**
   * Module initialization
   */
  onModuleInit(): void {
    this.isRunning = true;
    this.logger.log(`Worker initialized successfully`);
  }

  /**
   * Abstract method for processing jobs - must be implemented by subclasses
   * This method should be decorated with @Process() in the subclass
   */
  protected abstract processJob(job: Job<T>): Promise<JobResult<unknown>>;

  /**
   * Job wrapper with error handling and progress tracking
   */
  protected async processJobWrapper(job: Job<T>): Promise<JobResult<unknown>> {
    const startTime = this.dateTimeUtils.getTime();

    try {
      this.logger.log(`Processing job '${job.name}' with ID: ${job.id}`);

      const result = await this.processJob(job);

      this.lastProcessedAt = this.dateTimeUtils.getUtcNow();
      this.lastError = undefined;
      this.lastErrorAt = undefined;

      this.logger.log(`Job '${job.name}' (ID: ${job.id}) completed successfully`);
      this.onJobCompleted(job, result);

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.lastError = errorMessage;
      this.lastErrorAt = this.dateTimeUtils.getUtcNow();

      this.logger.error(`Job '${job.name}' (ID: ${job.id}) failed:`, error);
      this.onJobFailed(job, error as Error);

      throw error; // Re-throw to let BullMQ handle retries
    }
  }

  /**
   * Get worker health status
   */
  getHealthStatus(): WorkerHealthStatus {
    return {
      name: this.constructor.name,
      queueName: 'unknown', // Subclasses should override this
      isRunning: this.isRunning,
      concurrency: 1, // Default concurrency
      processing: 0, // Not available in base class
      lastProcessedAt: this.lastProcessedAt,
      lastError: this.lastError,
      lastErrorAt: this.lastErrorAt,
    };
  }

  /**
   * Hook methods for subclasses to override
   */
  protected onJobCompleted(_job: Job<T>, _result: JobResult<unknown>): void {
    // Override in subclasses if needed
  }

  protected onJobFailed(_job: Job<T>, _error: Error): void {
    // Override in subclasses if needed
  }

  protected onJobStalled(_jobId: string): void {
    // Override in subclasses if needed
  }

  protected onJobProgress(_job: Job<T>, _progress: number | object | string): void {
    // Override in subclasses if needed
  }

  protected onWorkerError(_error: Error): void {
    // Override in subclasses if needed
  }

  /**
   * Cleanup on module destroy
   */
  onModuleDestroy(): void {
    this.isRunning = false;
    this.logger.log('Worker cleanup completed');
  }
}
