import { Module, Global } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { EnvService } from '../env/env.service';
import { QueueService } from './queue.service';
import { QueueHealthService } from './queue-health.service';
import { QueueHealthMonitorService } from './queue-health-monitor.service';
import { BullBoardService } from './bullboard.service';
import { createQueueConnection } from './queue.config';

/**
 * Global Queue Module for BullMQ integration with @nestjs/bullmq
 * Provides queue infrastructure and services across the application
 */
@Global()
@Module({
  imports: [
    BullModule.forRootAsync({
      inject: [EnvService],
      useFactory: (envService: EnvService) => {
        const connectionConfig = createQueueConnection(envService);
        return {
          connection: {
            host: connectionConfig.host,
            port: connectionConfig.port,
            username: connectionConfig.username,
            password: connectionConfig.password,
            db: connectionConfig.db,
            maxRetriesPerRequest: connectionConfig.maxRetriesPerRequest,
            retryDelayOnFailover: connectionConfig.retryDelayOnFailover,
            enableReadyCheck: connectionConfig.enableReadyCheck,
            lazyConnect: connectionConfig.lazyConnect,
            keepAlive: connectionConfig.keepAlive,
            family: connectionConfig.family,
            keyPrefix: connectionConfig.keyPrefix,
          },
        };
      },
    }),
  ],
  providers: [QueueService, QueueHealthService, QueueHealthMonitorService, BullBoardService],
  exports: [QueueService, QueueHealthService, QueueHealthMonitorService, BullBoardService, BullModule],
})
export class QueueModule {}
