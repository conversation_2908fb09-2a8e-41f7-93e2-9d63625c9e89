import { Injectable, Logger, OnModuleD<PERSON>roy } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, Job, JobsOptions, RepeatOptions } from 'bullmq';
import { DateTimeUtilsService } from '@app/utils';
import { QueueError, type QueueHealthStatus } from './queue.error';
import { type JobDataType, JobDataSchema, QUEUE_PRIORITY_MAP, type QueuePriorityType } from './queue.config';

/**
 * Abstract base class for queue services
 * Provides common queue operations and standardized error handling
 * Now uses @nestjs/bullmq for queue injection
 */
@Injectable()
export abstract class BaseQueueService<T extends Record<string, unknown> = Record<string, unknown>>
  implements OnModuleDestroy
{
  protected readonly logger = new Logger(this.constructor.name);
  protected queue: Queue<T>;

  constructor(
    queue: Queue<T>,
    protected readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    this.queue = queue;
  }

  /**
   * Get queue name for logging and identification
   */
  getQueueName(): string {
    return this.queue.name;
  }

  /**
   * Add a job to the queue
   */
  async addJob(jobName: string, data: T, options?: Partial<JobsOptions>): Promise<Job<T>> {
    try {
      // Validate job data
      this.validateJobData({ data, opts: options || {} });

      // Use type assertion to work around BullMQ v5+ complex typing
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const job = await (this.queue as any).add(jobName, data, options);

      this.logger.log(`Job '${jobName}' added to queue '${this.getQueueName()}' with ID: ${job.id}`);
      return job;
    } catch (error) {
      this.logger.error(`Failed to add job '${jobName}' to queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOB_ADD_FAILED', {
        message: `Failed to add job '${jobName}' to queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), jobName, data },
      });
    }
  }

  /**
   * Add a job with priority
   */
  async addJobWithPriority(
    jobName: string,
    data: T,
    priority: QueuePriorityType,
    options?: Partial<JobsOptions>,
  ): Promise<Job<T>> {
    const priorityValue = QUEUE_PRIORITY_MAP[priority];
    return this.addJob(jobName, data, { ...options, priority: priorityValue });
  }

  /**
   * Add a delayed job
   */
  async addDelayedJob(jobName: string, data: T, delayMs: number, options?: Partial<JobsOptions>): Promise<Job<T>> {
    return this.addJob(jobName, data, { ...options, delay: delayMs });
  }

  /**
   * Add a repeating job
   */
  async addRepeatingJob(
    jobName: string,
    data: T,
    repeatOptions: RepeatOptions,
    options?: Partial<JobsOptions>,
  ): Promise<Job<T>> {
    return this.addJob(jobName, data, { ...options, repeat: repeatOptions });
  }

  /**
   * Get a job by ID
   */
  async getJob(jobId: string): Promise<Job<T> | undefined> {
    try {
      return await this.queue.getJob(jobId);
    } catch (error) {
      this.logger.error(`Failed to get job '${jobId}' from queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOB_GET_FAILED', {
        message: `Failed to get job '${jobId}' from queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), jobId },
      });
    }
  }

  /**
   * Remove a job by ID
   */
  async removeJob(jobId: string): Promise<void> {
    try {
      const job = await this.getJob(jobId);
      if (job) {
        await job.remove();
        this.logger.log(`Job '${jobId}' removed from queue '${this.getQueueName()}'`);
      }
    } catch (error) {
      this.logger.error(`Failed to remove job '${jobId}' from queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOB_REMOVE_FAILED', {
        message: `Failed to remove job '${jobId}' from queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), jobId },
      });
    }
  }

  /**
   * Pause the queue
   */
  async pauseQueue(): Promise<void> {
    try {
      await this.queue.pause();
      this.logger.log(`Queue '${this.getQueueName()}' paused`);
    } catch (error) {
      this.logger.error(`Failed to pause queue '${this.getQueueName()}':`, error);
      throw new QueueError('QUEUE_PAUSE_FAILED', {
        message: `Failed to pause queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName() },
      });
    }
  }

  /**
   * Resume the queue
   */
  async resumeQueue(): Promise<void> {
    try {
      await this.queue.resume();
      this.logger.log(`Queue '${this.getQueueName()}' resumed`);
    } catch (error) {
      this.logger.error(`Failed to resume queue '${this.getQueueName()}':`, error);
      throw new QueueError('QUEUE_RESUME_FAILED', {
        message: `Failed to resume queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName() },
      });
    }
  }

  /**
   * Get queue health status
   */
  async getHealthStatus(): Promise<QueueHealthStatus> {
    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getCompleted(),
        this.queue.getFailed(),
        this.queue.getDelayed(),
      ]);

      const isPaused = await this.queue.isPaused();

      return {
        name: this.getQueueName(),
        isHealthy: true,
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        paused: isPaused,
      };
    } catch (error) {
      this.logger.error(`Failed to get health status for queue '${this.getQueueName()}':`, error);
      return {
        name: this.getQueueName(),
        isHealthy: false,
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        paused: false,
        lastError: error instanceof Error ? error.message : 'Unknown error',
        lastErrorAt: this.dateTimeUtils.getUtcNow(),
      };
    }
  }

  /**
   * Clean completed and failed jobs
   */
  async cleanQueue(olderThanMs: number = 24 * 60 * 60 * 1000): Promise<void> {
    try {
      await Promise.all([this.queue.clean(olderThanMs, 100, 'completed'), this.queue.clean(olderThanMs, 50, 'failed')]);
      this.logger.log(`Queue '${this.getQueueName()}' cleaned successfully`);
    } catch (error) {
      this.logger.error(`Failed to clean queue '${this.getQueueName()}':`, error);
      throw new QueueError('QUEUE_CLEAN_FAILED', {
        message: `Failed to clean queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName() },
      });
    }
  }

  /**
   * Validate job data using Zod schema
   */
  private validateJobData(data: unknown): JobDataType {
    try {
      return JobDataSchema.parse(data);
    } catch (error) {
      throw new QueueError('INVALID_JOB_DATA', {
        message: 'Invalid job data provided',
        cause: error,
        context: { queueName: this.getQueueName(), data },
      });
    }
  }

  /**
   * Get the queue instance (for advanced operations)
   */
  getQueue(): Queue<T> {
    return this.queue;
  }

  /**
   * Cleanup resources on module destroy
   */
  async onModuleDestroy(): Promise<void> {
    try {
      await this.queue.close();
      this.logger.log(`Queue '${this.getQueueName()}' closed successfully`);
    } catch (error) {
      this.logger.error(`Error closing queue '${this.getQueueName()}':`, error);
    }
  }
}
