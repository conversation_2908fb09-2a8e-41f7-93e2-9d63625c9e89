import { Module } from '@nestjs/common';
import { BrokerModule as BrokerLibModule } from '@app/broker';
import { AuthModule } from '@app/auth';
import { BrokerController } from './broker.controller';

/**
 * API Broker Module
 *
 * Provides HTTP API endpoints for broker credential management.
 * Acts as the gateway between external clients and the broker library.
 *
 * Features:
 * - REST API endpoints for broker CRUD operations
 * - Authentication and authorization integration
 * - Request validation and response formatting
 * - Comprehensive error handling and logging
 * - Role-based access control
 *
 * Dependencies:
 * - BrokerLibModule: Core broker business logic and data access
 * - AuthModule: Authentication and authorization services
 */
@Module({
  imports: [BrokerLibModule, AuthModule],
  controllers: [BrokerController],
})
export class BrokerModule {}
