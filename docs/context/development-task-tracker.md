# PatternTrade API Development Task Tracker

This document tracks upcoming development tasks for the PatternTrade API project. Use this as a persistent reference for planning, prioritization, and context switching.

---

## August 10, 2025
- [x] Add Zerodha Kite login. remove public endpoint for broker login.
- [ ] Make the datastore module work with gRPC and symbol module.
- [ ] Integrate scanner module with Chartink scanner.
- [x] Utilise cls module
- [x] Add nestjs-bullmq
- [x] get userid from cls instead of dto object
- [ ] make kiteconnection instance a singleton
- [ ] maintain holiday calender


---

_Last updated: 10 August 2025_


the typical flow will be like in 2 ways.

manual way
    1. user logged in from browser to pattern trade app and established a session
    2. user clicks on login to zerodha from pattern trade app
    3. user is redirected to zerodha login page
    4. user logs in to zerodha
    5. user is redirected back to pattern trade app
    6. user is now logged in to pattern trade app and zerodha
    7. user can now use pattern trade app
    8. user can click download symbol master button
        8.1. this reqeust is sent to api gateway controller
        8.2. api controller sends a grpc request to datastore app controller
        8.3. datastore app sends a request to symbol module
        8.4. symbol module adds a job to bullmq queue
        8.5. bullmq queue processes the job
        8.6. bullmq queue calls symbol module to process the job
        8.7. symbol module downloads the symbol master from zerodha
        8.8. symbol module stores the symbol master in questdb

automated way
    1. at application bootup, bullmq queue checks if there is a job to download symbol master
    2. if there is no job, it adds a job to download symbol master at 8am IST daily on monday to friday.
    3. bullmq queue processes the job
    4. bullmq queue calls symbol module to process the job
    5. symbol module downloads the symbol master from zerodha
    6. symbol module stores the symbol master in questdb